// File: lib/core/providers/settings_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/date_format.dart';
import '../shared/models/theme_mode.dart';
import '../models/settings_state.dart';
import '../di/service_locator.dart';

import '../providers/localization_provider.dart';
import '../utils/event_bus.dart';
import '../services/background_monitoring_service.dart';
import '../services/alert_coordination_service.dart';
import '../../features/setup/domain/models/setup_preferences.dart' as domain;
import '../../features/meter_readings/domain/models/meter_reading.dart';
import 'database_provider.dart';
import '../utils/logger.dart';

part 'settings_provider.g.dart';

/// Result of setup completion operation
class SetupCompletionResult {
  final bool success;
  final bool meterReadingCreated;
  final String? error;

  SetupCompletionResult({
    required this.success,
    required this.meterReadingCreated,
    this.error,
  });

  bool get hasError => error != null;
}

/// Provider for comprehensive settings management using Riverpod
@riverpod
class Settings extends _$Settings {
  @override
  Future<SettingsState> build() async {
    return await _loadSettings();
  }

  /// Load settings from SharedPreferences
  Future<SettingsState> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return SettingsState(
        language: prefs.getString(PreferenceKeys.language) ?? 'English',
        currency: prefs.getString(PreferenceKeys.currency) ?? 'GBP',
        currencySymbol: prefs.getString(PreferenceKeys.currencySymbol) ?? '£',
        alertThreshold: prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        daysInAdvance: prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
        notificationsEnabled:
            prefs.getBool(PreferenceKeys.notificationsEnabled) ?? false,
        remindersEnabled:
            prefs.getBool(PreferenceKeys.remindersEnabled) ?? false,
        lowBalanceAlertsEnabled:
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        timeToTopUpAlertsEnabled:
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        invalidRecordAlertsEnabled:
            prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false,
        reminderFrequency:
            prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly',
        reminderStartDateTime: _parseDateTime(
            prefs.getString(PreferenceKeys.reminderStartDateTime)),
        dateFormat: prefs.getString(PreferenceKeys.dateFormat) ?? 'dd-MM-yyyy',
        showTimeWithDate:
            prefs.getBool(PreferenceKeys.showTimeWithDate) ?? false,
        themeMode: _parseThemeMode(prefs.getString(PreferenceKeys.themeMode)),
      );
    } catch (e) {
      return const SettingsState();
    }
  }

  /// Parse DateTime from string
  DateTime? _parseDateTime(String? dateTimeString) {
    if (dateTimeString == null) return null;
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Parse theme mode from string
  AppThemeMode _parseThemeMode(String? themeModeString) {
    if (themeModeString == null) return AppThemeMode.system;
    try {
      return AppThemeMode.values.firstWhere(
        (mode) => mode.toString() == themeModeString,
        orElse: () => AppThemeMode.system,
      );
    } catch (e) {
      return AppThemeMode.system;
    }
  }

  /// Update language
  Future<void> updateLanguage(String language) async {
    final currentState = await future;
    if (currentState.language == language) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, language);

      try {
        final localizationProvider = serviceLocator<LocalizationProvider>();
        await localizationProvider.setLocale(_getLanguageCode(language));
      } catch (e) {
        // Ignore localization provider errors for now
      }

      state = AsyncValue.data(currentState.copyWith(language: language));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Convert language name to language code
  String _getLanguageCode(String language) {
    switch (language) {
      case 'English':
        return 'en';
      case 'Spanish':
        return 'es';
      case 'French':
        return 'fr';
      case 'German':
        return 'de';
      case 'Italian':
        return 'it';
      case 'Portuguese':
        return 'pt';
      case 'Russian':
        return 'ru';
      case 'Chinese':
        return 'zh';
      case 'Japanese':
        return 'ja';
      case 'Hindi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// Update currency
  Future<void> updateCurrency(String currency, String symbol) async {
    final currentState = await future;
    if (currentState.currency == currency &&
        currentState.currencySymbol == symbol) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.currency, currency);
      await prefs.setString(PreferenceKeys.currencySymbol, symbol);

      state = AsyncValue.data(currentState.copyWith(
        currency: currency,
        currencySymbol: symbol,
      ));

      // Fire event for currency update
      EventBus().fire(EventType.currencyUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update alert threshold
  Future<void> updateAlertThreshold(double threshold) async {
    final currentState = await future;
    if (currentState.alertThreshold == threshold) return;

    if (threshold < 1.00 || threshold > 999.99) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(PreferenceKeys.alertThreshold, threshold);

      state = AsyncValue.data(currentState.copyWith(alertThreshold: threshold));

      // Fire event for alert settings update
      EventBus().fire(EventType.alertSettingsUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update days in advance
  Future<void> updateDaysInAdvance(int days) async {
    final currentState = await future;
    if (currentState.daysInAdvance == days) return;

    if (days < 0 || days > 99) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(PreferenceKeys.daysInAdvance, days);

      state = AsyncValue.data(currentState.copyWith(daysInAdvance: days));

      // Fire event for alert settings update
      EventBus().fire(EventType.alertSettingsUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update date format
  Future<void> updateDateFormat(DateFormat format) async {
    final formatString = format.formatString;
    await updateDateFormatFromString(formatString);
  }

  /// Update date format from string (for backward compatibility)
  Future<void> updateDateFormatFromString(String formatString) async {
    final currentState = await future;
    if (currentState.dateFormat == formatString) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.dateFormat, formatString);

      state = AsyncValue.data(currentState.copyWith(dateFormat: formatString));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update show time with date
  Future<void> updateShowTimeWithDate(bool show) async {
    final currentState = await future;
    if (currentState.showTimeWithDate == show) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.showTimeWithDate, show);

      state = AsyncValue.data(currentState.copyWith(showTimeWithDate: show));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update notifications enabled
  Future<void> updateNotificationsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.notificationsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.notificationsEnabled, enabled);

      state =
          AsyncValue.data(currentState.copyWith(notificationsEnabled: enabled));

      // Fire event for notification settings update
      EventBus().fire(EventType.notificationSettingsUpdated);

      // Update background monitoring
      try {
        final backgroundService = BackgroundMonitoringService();
        await backgroundService.updateMonitoring();
      } catch (e) {
        // Continue if background service fails
      }

      // Trigger alert check and update predictions
      try {
        final alertService = AlertCoordinationService();
        await alertService.checkAlertsAndUpdatePredictions();
      } catch (e) {
        // Continue if alert service fails
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update reminders enabled
  Future<void> updateRemindersEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.remindersEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.remindersEnabled, enabled);

      if (!enabled) {
        await updateReminderStartDateTime(null);
      }

      state = AsyncValue.data(currentState.copyWith(remindersEnabled: enabled));

      // Fire event for reminder settings update
      EventBus().fire(EventType.reminderSettingsUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update low balance alerts enabled
  Future<void> updateLowBalanceAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.lowBalanceAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.lowBalanceAlertsEnabled, enabled);

      state = AsyncValue.data(
          currentState.copyWith(lowBalanceAlertsEnabled: enabled));

      // Fire event for notification settings update
      EventBus().fire(EventType.notificationSettingsUpdated);

      // Update background monitoring
      try {
        final backgroundService = BackgroundMonitoringService();
        await backgroundService.updateMonitoring();
      } catch (e) {
        // Continue if background service fails
      }

      // Trigger alert check
      try {
        final alertService = AlertCoordinationService();
        await alertService.checkAllAlerts();
      } catch (e) {
        // Continue if alert service fails
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update time to top up alerts enabled
  Future<void> updateTimeToTopUpAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.timeToTopUpAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.timeToTopUpAlertsEnabled, enabled);

      state = AsyncValue.data(
          currentState.copyWith(timeToTopUpAlertsEnabled: enabled));

      // Fire event for notification settings update
      EventBus().fire(EventType.notificationSettingsUpdated);

      // Update background monitoring
      try {
        final backgroundService = BackgroundMonitoringService();
        await backgroundService.updateMonitoring();
      } catch (e) {
        // Continue if background service fails
      }

      // Trigger alert check and update predictions
      try {
        final alertService = AlertCoordinationService();
        await alertService.checkAlertsAndUpdatePredictions();
      } catch (e) {
        // Continue if alert service fails
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update invalid record alerts enabled
  Future<void> updateInvalidRecordAlertsEnabled(bool enabled) async {
    final currentState = await future;
    if (currentState.invalidRecordAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.invalidRecordAlertsEnabled, enabled);

      state = AsyncValue.data(
          currentState.copyWith(invalidRecordAlertsEnabled: enabled));

      // Fire event for notification settings update
      EventBus().fire(EventType.notificationSettingsUpdated);

      // Update background monitoring
      try {
        final backgroundService = BackgroundMonitoringService();
        await backgroundService.updateMonitoring();
      } catch (e) {
        // Continue if background service fails
      }

      // Trigger alert check and update predictions
      try {
        final alertService = AlertCoordinationService();
        await alertService.checkAlertsAndUpdatePredictions();
      } catch (e) {
        // Continue if alert service fails
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update reminder frequency
  Future<void> updateReminderFrequency(String frequency) async {
    final currentState = await future;
    if (currentState.reminderFrequency == frequency) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.reminderFrequency, frequency);

      state =
          AsyncValue.data(currentState.copyWith(reminderFrequency: frequency));

      // Fire event for reminder settings update
      EventBus().fire(EventType.reminderSettingsUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update reminder start date time
  Future<void> updateReminderStartDateTime(DateTime? dateTime) async {
    final currentState = await future;
    if (currentState.reminderStartDateTime == dateTime) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      if (dateTime != null) {
        await prefs.setString(
            PreferenceKeys.reminderStartDateTime, dateTime.toIso8601String());
      } else {
        await prefs.remove(PreferenceKeys.reminderStartDateTime);
      }

      state = AsyncValue.data(
          currentState.copyWith(reminderStartDateTime: dateTime));

      // Fire event for reminder settings update
      EventBus().fire(EventType.reminderSettingsUpdated);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode mode) async {
    final currentState = await future;
    if (currentState.themeMode == mode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.themeMode, mode.toString());

      state = AsyncValue.data(currentState.copyWith(themeMode: mode));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Complete setup process with Riverpod providers
  Future<SetupCompletionResult> completeSetup(
      domain.SetupPreferences preferences) async {
    try {
      Logger.info('SettingsProvider: Starting setup completion');

      // Step 1: Save all preferences to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, preferences.language);
      await prefs.setString(PreferenceKeys.currency, preferences.currency);
      await prefs.setString(
          PreferenceKeys.currencySymbol, preferences.currencySymbol);
      await prefs.setDouble(
          PreferenceKeys.alertThreshold, preferences.alertThreshold);
      await prefs.setInt(
          PreferenceKeys.daysInAdvance, preferences.daysInAdvance);
      await prefs.setString(
          PreferenceKeys.dateFormat, preferences.dateFormat.formatString);
      await prefs.setBool(
          PreferenceKeys.showTimeWithDate, preferences.showTimeWithDate);
      await prefs.setString(
          PreferenceKeys.themeMode, preferences.themeMode.toString());

      // Step 2: Create initial meter reading if provided
      bool meterReadingSuccess = true;
      String? meterReadingError;

      if (preferences.initialMeterReading != null) {
        Logger.info(
            'SettingsProvider: Creating initial meter reading with value ${preferences.initialMeterReading}');

        // Validate initial meter reading value (0.0 to 999.99)
        if (preferences.initialMeterReading! < 0.0 ||
            preferences.initialMeterReading! > 999.99) {
          meterReadingSuccess = false;
          meterReadingError =
              'Initial meter reading must be between 0.00 and 999.99';
          Logger.error(
              'SettingsProvider: Invalid initial reading value: ${preferences.initialMeterReading}');
        } else {
          try {
            final meterReading = MeterReading(
              value: preferences.initialMeterReading!,
              date: DateTime.now(),
              notes: 'Initial meter reading from setup',
            );

            final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
            final result = await meterReadingRepo.addMeterReading(meterReading);

            if (result > 0) {
              Logger.info(
                  'SettingsProvider: Successfully created initial meter reading');
              EventBus().fire(EventType.dataUpdated);
            } else {
              meterReadingSuccess = false;
              meterReadingError = 'Failed to create initial meter reading';
              Logger.warning(
                  'SettingsProvider: Initial meter reading creation failed');
            }
          } catch (e) {
            meterReadingSuccess = false;
            meterReadingError = 'Exception creating initial meter reading: $e';
            Logger.error(
                'SettingsProvider: Exception creating initial meter reading: $e');
          }
        }
      }

      // Step 3: Mark setup as completed
      await prefs.setBool(PreferenceKeys.setupCompleted, true);
      Logger.info('SettingsProvider: Setup marked as completed');

      // Step 4: Update provider state
      state = AsyncValue.data(SettingsState(
        language: preferences.language,
        currency: preferences.currency,
        currencySymbol: preferences.currencySymbol,
        alertThreshold: preferences.alertThreshold,
        daysInAdvance: preferences.daysInAdvance,
        dateFormat: preferences.dateFormat.formatString,
        showTimeWithDate: preferences.showTimeWithDate,
        themeMode: preferences.themeMode,
      ));

      return SetupCompletionResult(
        success: true,
        meterReadingCreated: meterReadingSuccess,
        error: meterReadingError,
      );
    } catch (e) {
      Logger.error('SettingsProvider: Setup completion failed: $e');
      return SetupCompletionResult(
        success: false,
        meterReadingCreated: false,
        error: 'Setup completion failed: ${e.toString()}',
      );
    }
  }
}
