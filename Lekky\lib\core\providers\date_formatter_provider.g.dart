// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'date_formatter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dateFormatterHash() => r'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0';

/// Provider for settings-aware date formatting
///
/// Copied from [dateFormatter].
@ProviderFor(dateFormatter)
final dateFormatterProvider = AutoDisposeProvider<DateFormatterService>.internal(
  dateFormatter,
  name: r'dateFormatterProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dateFormatterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DateFormatterRef = AutoDisposeProviderRef<DateFormatterService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
