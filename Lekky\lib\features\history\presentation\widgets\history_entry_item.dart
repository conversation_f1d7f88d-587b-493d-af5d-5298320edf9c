import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/providers/date_formatter_provider.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../entries/presentation/dialogs/edit_entry_dialog.dart';
import '../../../entries/presentation/dialogs/delete_confirmation_dialog.dart';
import '../../../entries/presentation/controllers/entry_controller.dart';
import '../../../validation/domain/models/validation_issue.dart';
import '../../../validation/domain/services/data_integrity_service.dart';

/// A widget that displays a single entry in the History screen
class HistoryEntryItem extends ConsumerWidget {
  /// The entry to display (either a MeterReading or a TopUp)
  final dynamic entry;

  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when an entry is updated
  final VoidCallback onEntryUpdated;

  /// Callback when an entry is deleted
  final VoidCallback onEntryDeleted;

  /// Constructor
  const HistoryEntryItem({
    super.key,
    required this.entry,
    this.currencySymbol = '₦',
    required this.onEntryUpdated,
    required this.onEntryDeleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final bool isMeterReading = entry is MeterReading;

    // Determine icon, color, and text based on entry type
    IconData icon;
    Color iconColor;
    String title;
    String value;

    if (isMeterReading) {
      icon = Icons.speed;
      iconColor = theme.colorScheme.primary;
      title = 'Meter Reading';
      value =
          '$currencySymbol${(entry as MeterReading).value.toStringAsFixed(2)}';
    } else {
      icon = Icons.add_card;
      iconColor = theme.colorScheme.secondary;
      title = 'Top-up';
      value = '$currencySymbol${(entry as TopUp).amount.toStringAsFixed(2)}';
    }

    return Dismissible(
      key: ValueKey(isMeterReading
          ? 'meter_reading_${(entry as MeterReading).id}'
          : 'top_up_${(entry as TopUp).id}'),
      background: Container(
        color: theme.colorScheme.primary.withOpacity(0.2),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: Icon(
          Icons.edit,
          color: theme.colorScheme.primary,
        ),
      ),
      secondaryBackground: Container(
        color: theme.colorScheme.error.withOpacity(0.2),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: Icon(
          Icons.delete,
          color: theme.colorScheme.error,
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // Edit entry
          _showEditDialog(context);
          return false;
        } else {
          // Delete entry - show delete confirmation dialog
          _showDeleteConfirmationDialog(context);
          return false;
        }
      },
      child: InkWell(
        onTap: () => _showEditDialog(context),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
          ),
          child: _buildResponsiveRow(
              context, icon, iconColor, title, value, theme, isMeterReading),
        ),
      ),
    );
  }

  /// Build responsive row layout with fixed column widths
  Widget _buildResponsiveRow(
      BuildContext context,
      IconData icon,
      Color iconColor,
      String title,
      String value,
      ThemeData theme,
      bool isMeterReading) {
    final screenWidth = MediaQuery.of(context).size.width;
    const containerPadding = 32.0; // 16px padding on each side
    final totalWidth = screenWidth - containerPadding;

    // Responsive width calculations
    const iconWidth = 56.0; // Fixed icon area width
    final availableWidth =
        totalWidth - iconWidth - 16.0; // Account for SizedBox

    // Adaptive column proportions based on screen size
    double titleWidthRatio, amountWidthRatio, spacerWidthRatio;

    if (screenWidth < 360) {
      // Small screens: more space for title, less for spacer
      titleWidthRatio = 0.65;
      amountWidthRatio = 0.30;
      spacerWidthRatio = 0.05;
    } else if (screenWidth > 600) {
      // Large screens: more balanced spacing
      titleWidthRatio = 0.55;
      amountWidthRatio = 0.25;
      spacerWidthRatio = 0.20;
    } else {
      // Medium screens: default proportions
      titleWidthRatio = 0.60;
      amountWidthRatio = 0.25;
      spacerWidthRatio = 0.15;
    }

    final titleWidth = availableWidth * titleWidthRatio;
    final amountWidth = availableWidth * amountWidthRatio;
    final spacerWidth = availableWidth * spacerWidthRatio;

    return Row(
      children: [
        // Icon area with fixed width
        SizedBox(
          width: iconWidth,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Title and date area with calculated width
        SizedBox(
          width: titleWidth,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.onSurface,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if ((isMeterReading && (entry as MeterReading).isInvalid) ||
                      (!isMeterReading && (entry as TopUp).isInvalid))
                    _buildValidationIndicator(context, ref),
                ],
              ),
              Text(
                ref.watch(dateFormatterProvider).formatDateForHistory(
                      isMeterReading
                          ? (entry as MeterReading).date
                          : (entry as TopUp).date,
                    ),
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if ((isMeterReading && (entry as MeterReading).notes != null) ||
                  (!isMeterReading && (entry as TopUp).notes != null))
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    isMeterReading
                        ? (entry as MeterReading).notes!
                        : (entry as TopUp).notes!,
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface.withOpacity(0.8),
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
        ),
        // Amount area with calculated width (centered position)
        SizedBox(
          width: amountWidth,
          child: Container(
            alignment: Alignment.centerRight,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getAmountTextColor(theme, isMeterReading),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // Right spacer to create the desired margin from edge
        SizedBox(width: spacerWidth),
      ],
    );
  }

  /// Get the text color for the amount based on entry status
  Color _getAmountTextColor(ThemeData theme, bool isMeterReading) {
    if (isMeterReading) {
      final meterReading = entry as MeterReading;
      if (meterReading.isInvalid) return Colors.amber;
      return theme.colorScheme.onSurface;
    } else {
      final topUp = entry as TopUp;
      if (topUp.isInvalid) return Colors.amber;
      return theme.colorScheme.onSurface;
    }
  }

  /// Build the validation indicator for invalid entries
  Widget _buildValidationIndicator(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Tooltip(
            message: 'This reading may be invalid. Tap to edit.',
            child: const Icon(
              Icons.warning_amber,
              color: Colors.amber,
              size: 16,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: InkWell(
            onTap: () => _showQuickFixDialog(context, ref),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber),
              ),
              child: const Text(
                'Fix',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  /// Build the dialog header with close button
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber,
            color: Colors.amber,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'Fix Invalid Reading',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  /// Format issue message to show "expected less than" for balance inconsistency
  String _formatIssueMessage(ValidationIssue issue) {
    if (issue.type == ValidationIssueType.balanceInconsistency &&
        issue.metadata != null &&
        issue.metadata!.containsKey('expected_value')) {
      final expectedValue = issue.metadata!['expected_value'] as double;
      return 'Balance inconsistency detected: expected less than $currencySymbol${expectedValue.toStringAsFixed(2)}';
    }
    return issue.message;
  }

  /// Show a quick fix dialog for invalid entries
  void _showQuickFixDialog(BuildContext context, WidgetRef ref) {
    if (entry is! MeterReading) return;

    final theme = Theme.of(context);
    final meterReading = entry as MeterReading;
    final parentContext = context; // Capture parent context

    // Get the data integrity service
    final dataIntegrityService = serviceLocator<DataIntegrityService>();

    showDialog(
      context: context,
      builder: (dialogContext) => FutureBuilder<List<ValidationIssue>>(
        future: dataIntegrityService.validateSingleEntry(meterReading),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            final screenWidth = MediaQuery.of(context).size.width;
            final dialogWidth = _getDialogWidth(context);
            final horizontalPadding = (screenWidth - dialogWidth) / 2;

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 24,
              insetPadding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: 28,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDialogHeader(context),
                  const Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                ],
              ),
            );
          }

          final issues = snapshot.data ?? [];
          final screenWidth = MediaQuery.of(context).size.width;
          final dialogWidth = _getDialogWidth(context);
          final horizontalPadding = (screenWidth - dialogWidth) / 2;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 24,
            insetPadding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: 28,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDialogHeader(context),
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (issues.isEmpty)
                        Text(
                          'This reading is marked as invalid, but no specific issues were found. It may have been manually marked as invalid.',
                          style: TextStyle(
                            fontSize: 14,
                            color: theme.colorScheme.onSurface,
                          ),
                        )
                      else
                        ...issues.map((issue) {
                          IconData icon;
                          Color color;

                          switch (issue.severity) {
                            case ValidationIssueSeverity.high:
                              icon = Icons.error;
                              color = Colors.red;
                              break;
                            case ValidationIssueSeverity.medium:
                              icon = Icons.warning;
                              color = Colors.orange;
                              break;
                            case ValidationIssueSeverity.low:
                              icon = Icons.info;
                              color = Colors.blue;
                              break;
                          }

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(icon, color: color, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _formatIssueMessage(issue),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: theme.colorScheme.onSurface,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      const SizedBox(height: 16),
                      Text(
                        'Meter Reading: $currencySymbol${meterReading.value.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        'Date: ${ref.watch(dateFormatterProvider).formatDateForHistory(meterReading.date)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Button layout matching edit entry dialog
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: LekkyButton(
                              text: 'Delete',
                              type: LekkyButtonType.destructive,
                              size: LekkyButtonSize.compact,
                              onPressed: () async {
                                Navigator.of(dialogContext).pop();
                                if (parentContext.mounted) {
                                  await _showDeleteConfirmationDialog(
                                      parentContext);
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LekkyButton(
                              text: 'Cancel',
                              type: LekkyButtonType.secondary,
                              size: LekkyButtonSize.compact,
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: LekkyButton(
                              text: 'Edit Entry',
                              type: LekkyButtonType.special,
                              size: LekkyButtonSize.compact,
                              onPressed: () {
                                Navigator.of(dialogContext).pop();
                                _showEditDialog(context);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Show the edit dialog
  void _showEditDialog(BuildContext context) {
    final bool isMeterReading = entry is MeterReading;

    if (isMeterReading) {
      final meterReading = entry as MeterReading;

      showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: meterReading,
          topUp: null,
          currencySymbol: currencySymbol,
          onEntryUpdated: onEntryUpdated,
          onEntryDeleted: onEntryDeleted,
        ),
      );
    } else {
      final topUp = entry as TopUp;

      showDialog(
        context: context,
        builder: (context) => EditEntryDialog(
          meterReading: null,
          topUp: topUp,
          currencySymbol: currencySymbol,
          onEntryUpdated: onEntryUpdated,
          onEntryDeleted: onEntryDeleted,
        ),
      );
    }
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmationDialog(BuildContext context) async {
    final bool isMeterReading = entry is MeterReading;

    if (isMeterReading) {
      final meterReading = entry as MeterReading;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => DeleteConfirmationDialog(
          entryType: EntryType.meterReading,
          value: meterReading.value,
          date: meterReading.date,
          currencySymbol: currencySymbol,
        ),
      );

      if (confirmed == true && context.mounted) {
        await _performDeletion(context, meterReading: meterReading);
      }
    } else {
      final topUp = entry as TopUp;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => DeleteConfirmationDialog(
          entryType: EntryType.topUp,
          value: topUp.amount,
          date: topUp.date,
          currencySymbol: currencySymbol,
        ),
      );

      if (confirmed == true && context.mounted) {
        await _performDeletion(context, topUp: topUp);
      }
    }
  }

  /// Perform the actual deletion using EntryController
  Future<void> _performDeletion(BuildContext context,
      {MeterReading? meterReading, TopUp? topUp}) async {
    try {
      // Get repositories from service locator
      final meterReadingRepository = serviceLocator<MeterReadingRepository>();
      final topUpRepository = serviceLocator<TopUpRepository>();

      // Create an EntryController instance with required repositories
      final entryController = EntryController(
        meterReadingRepository: meterReadingRepository,
        topUpRepository: topUpRepository,
      );

      bool success = false;

      if (meterReading != null) {
        success = await entryController.deleteMeterReading(meterReading.id!);
      } else if (topUp != null) {
        success = await entryController.deleteTopUp(topUp.id!);
      }

      if (!context.mounted) return;

      if (success) {
        // Call the callback to refresh the UI
        onEntryDeleted();
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(entryController.errorMessage ?? 'Failed to delete entry'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete entry: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
