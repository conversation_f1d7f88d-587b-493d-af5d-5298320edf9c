import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/settings_state.dart';
import '../services/date_formatter_service.dart';
import 'settings_provider.dart';

part 'date_formatter_provider.g.dart';

/// Provider for settings-aware date formatting
@riverpod
DateFormatterService dateFormatter(DateFormatterRef ref) {
  final settingsAsync = ref.watch(settingsProvider);
  
  return settingsAsync.when(
    data: (settings) => DateFormatterService(settings),
    loading: () => DateFormatterService.withDefaults(),
    error: (_, __) => DateFormatterService.withDefaults(),
  );
}
